/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto Mono', monospace;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInDown 1s ease-out;
}

.title {
    font-family: 'Orbitron', monospace;
    font-size: 3.5rem;
    font-weight: 900;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    margin-bottom: 10px;
    animation: glow 2s ease-in-out infinite alternate;
}

.tagline {
    font-size: 1.2rem;
    color: #f0f0f0;
    font-style: italic;
    opacity: 0.9;
}

/* Printer Section */
.printer-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
    align-items: start;
}

/* Printer Container */
.printer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.printer {
    background: linear-gradient(145deg, #e6e6e6, #ffffff);
    border-radius: 20px;
    box-shadow: 
        20px 20px 60px #bebebe,
        -20px -20px 60px #ffffff;
    padding: 30px;
    width: 100%;
    max-width: 400px;
    position: relative;
    transition: transform 0.3s ease;
}

.printer:hover {
    transform: translateY(-5px);
}

/* Printer Top Section */
.printer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 84px;
}

.printer-screen {
    display: flex;
    flex-wrap: wrap;
    background: #000;
    color: #00ff00;
    padding: 10px 15px;
    border-radius: 5px;
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    min-width: 150px;
    max-width:230px;
    text-align: center;
    box-shadow: inset 0 0 10px rgba(0, 255, 0, 0.3);
}

.printer-buttons {
    display: flex;
    gap: 10px;
}

.printer-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(145deg, #f0f0f0, #cacaca);
    box-shadow: 5px 5px 10px #bebebe, -5px -5px 10px #ffffff;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s ease;
}

.printer-btn:hover {
    transform: scale(1.1);
}

.printer-btn:active {
    box-shadow: inset 5px 5px 10px #bebebe, inset -5px -5px 10px #ffffff;
}

/* Printer Body */
.printer-body {
    background: #f5f5f5;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.paper-slot {
    height: 60px;
    background: linear-gradient(to bottom, #ddd, #fff);
    border-radius: 5px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.paper-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 2px;
    background: #999;
    opacity: 0.5;
}

.printer-output {
    min-height: 100px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}
/* Control Panel */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.currency-selector h3 {
    margin-bottom: 20px;
    color: #333;
    font-family: 'Orbitron', monospace;
}

.currency-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 30px;
}

.currency-btn {
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Roboto Mono', monospace;
    font-weight: 600;
}

.currency-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.currency-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Custom Currency Creator */
.custom-creator {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
    border: 2px dashed #667eea;
    animation: fadeIn 0.5s ease-out;
}

.custom-creator h3 {
    margin-bottom: 20px;
    color: #333;
    font-family: 'Orbitron', monospace;
    text-align: center;
}

.custom-form {
    display: grid;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.form-group input[type="text"],
.form-group input[type="file"] {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-family: 'Roboto Mono', monospace;
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.image-preview {
    margin-top: 10px;
    min-height: 60px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    position: relative;
}

.image-preview img {
    max-width: 100%;
    max-height: 60px;
    border-radius: 5px;
}

.image-preview.empty::after {
    content: "📷 Image preview will appear here";
    color: #999;
    font-size: 0.9rem;
}

.save-custom-btn {
    padding: 15px;
    background: linear-gradient(135deg, #10ac84, #00d2d3);
    color: white;
    border: none;
    border-radius: 10px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.save-custom-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 172, 132, 0.4);
}

.custom-preview {
    margin-top: 20px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    text-align: center;
}

.custom-preview h4 {
    margin-bottom: 10px;
    color: #555;
    font-family: 'Orbitron', monospace;
}

.preview-bill {
    width: 150px;
    height: 75px;
    margin: 0 auto;
    border: 2px solid #333;
    border-radius: 8px;
    padding: 8px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-size: 0.6rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    transform: scale(0.8);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Controls */
.print-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.print-btn, .pdf-btn {
    padding: 20px;
    border: none;
    border-radius: 15px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.print-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.print-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.pdf-btn {
    background: linear-gradient(135deg, #4834d4, #686de0);
    color: white;
    box-shadow: 0 10px 30px rgba(72, 52, 212, 0.4);
}

.pdf-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(72, 52, 212, 0.6);
}

.btn-icon {
    font-size: 1.5rem;
}

/* Warning Section */
.warning-section {
    margin-top: 40px;
}

.warning-box {
    background: linear-gradient(135deg, #ff9ff3, #f368e0);
    border-radius: 20px;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
    color: white;
    box-shadow: 0 20px 40px rgba(243, 104, 224, 0.3);
    font-size: 90%;
}

.warning-icon {
    font-size: 3rem;
    animation: pulse 2s infinite;
}

.warning-text h3 {
    font-family: 'Orbitron', monospace;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.warning-text p {
    margin-bottom: 5px;
    line-height: 1.4;
}

/* Sound Toggle */
.sound-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.sound-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    font-size: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.sound-btn:hover {
    transform: scale(1.1);
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 0 0 40px rgba(102, 126, 234, 0.5);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes printing {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}

.printing {
    animation: printing 0.5s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .printer-section {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .title {
        font-size: 2.5rem;
    }

    .currency-options {
        grid-template-columns: 1fr;
    }

    .warning-box {
        flex-direction: column;
        text-align: center;
    }

    .custom-creator {
        padding: 20px;
    }

    .custom-form {
        gap: 12px;
    }

    .form-group input[type="text"] {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}
