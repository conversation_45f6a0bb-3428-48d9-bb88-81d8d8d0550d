# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (if using build tools)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Firebase (these should be ignored if present)
.firebase/
firebase-debug.log
firestore-debug.log

# Note: firebase-config.js is NOT ignored because:
# 1. Firebase client credentials are safe to be public
# 2. They're required for the app to function
# 3. Real security comes from Firestore rules, not hiding these keys

# However, if you have server-side Firebase admin credentials, those should be ignored:
# firebase-admin-key.json
# service-account-key.json
