# Security settings for uploads directory

# Prevent execution of PHP files
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Only allow image files
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to all other file types
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Add cache headers for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# Add security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
</IfModule>
