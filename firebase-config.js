// Firebase Configuration
// Note: These credentials are safe to be public in client-side apps
// Firebase security comes from Firestore rules, not hiding these keys

const firebaseConfig = {
    // Option 1: Direct configuration (recommended for simplicity)
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"

    // Option 2: Environment variables (requires build tool like Vite/Webpack)
    // apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    // authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    // projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    // storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    // messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    // appId: import.meta.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase services
const db = firebase.firestore();
const auth = firebase.auth();
// Note: No Firebase Storage - using external image hosting instead

// Firestore collections
const COLLECTIONS = {
    BILLS: 'bills',
    USERS: 'users',
    LIKES: 'likes'
};

// Firebase helper functions
const FirebaseService = {
    // Initialize anonymous authentication
    async initAuth() {
        return new Promise((resolve) => {
            auth.onAuthStateChanged((user) => {
                if (user) {
                    resolve(user);
                } else {
                    // Sign in anonymously
                    auth.signInAnonymously().then(resolve).catch(console.error);
                }
            });
        });
    },

    // Save bill to Firestore
    async saveBill(billData) {
        try {
            const user = auth.currentUser;
            if (!user) throw new Error('User not authenticated');

            const billWithUser = {
                ...billData,
                userId: user.uid,
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            };

            const docRef = await db.collection(COLLECTIONS.BILLS).add(billWithUser);
            return { id: docRef.id, ...billWithUser };
        } catch (error) {
            console.error('Error saving bill:', error);
            throw error;
        }
    },

    // Get all bills (public feed)
    async getBills(limit = 50) {
        try {
            const snapshot = await db.collection(COLLECTIONS.BILLS)
                .orderBy('createdAt', 'desc')
                .limit(limit)
                .get();

            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Error getting bills:', error);
            return [];
        }
    },

    // Get user's bills
    async getUserBills(userId = null) {
        try {
            const user = userId || auth.currentUser?.uid;
            if (!user) return [];

            const snapshot = await db.collection(COLLECTIONS.BILLS)
                .where('userId', '==', user)
                .orderBy('createdAt', 'desc')
                .get();

            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            console.error('Error getting user bills:', error);
            return [];
        }
    },

    // Update bill likes
    async updateBillLikes(billId, increment = true) {
        try {
            const user = auth.currentUser;
            if (!user) throw new Error('User not authenticated');

            const billRef = db.collection(COLLECTIONS.BILLS).doc(billId);
            const likeRef = db.collection(COLLECTIONS.LIKES).doc(`${user.uid}_${billId}`);

            await db.runTransaction(async (transaction) => {
                const billDoc = await transaction.get(billRef);
                const likeDoc = await transaction.get(likeRef);

                if (!billDoc.exists) throw new Error('Bill not found');

                const currentLikes = billDoc.data().likes || 0;
                const isLiked = likeDoc.exists;

                if (increment && !isLiked) {
                    // Add like
                    transaction.update(billRef, { likes: currentLikes + 1 });
                    transaction.set(likeRef, {
                        userId: user.uid,
                        billId: billId,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp()
                    });
                } else if (!increment && isLiked) {
                    // Remove like
                    transaction.update(billRef, { likes: Math.max(0, currentLikes - 1) });
                    transaction.delete(likeRef);
                }
            });

            return true;
        } catch (error) {
            console.error('Error updating likes:', error);
            return false;
        }
    },

    // Check if user liked a bill
    async isLiked(billId) {
        try {
            const user = auth.currentUser;
            if (!user) return false;

            const likeDoc = await db.collection(COLLECTIONS.LIKES)
                .doc(`${user.uid}_${billId}`)
                .get();

            return likeDoc.exists;
        } catch (error) {
            console.error('Error checking like status:', error);
            return false;
        }
    },

    // Upload image to external hosting service
    async uploadImage(imageBlob, fileName) {
        try {
            // Option 1: ImgBB (Free API)
            return await this.uploadToImgBB(imageBlob);

            // Option 2: Cloudinary (Uncomment to use)
            // return await this.uploadToCloudinary(imageBlob, fileName);

        } catch (error) {
            console.error('Error uploading image:', error);
            throw error;
        }
    },

    // Upload to ImgBB (Free service)
    async uploadToImgBB(imageBlob) {
        const formData = new FormData();
        formData.append('image', imageBlob);

        // You can get a free API key from https://api.imgbb.com/
        const API_KEY = 'your-imgbb-api-key'; // Replace with your ImgBB API key

        const response = await fetch(`https://api.imgbb.com/1/upload?key=${API_KEY}`, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.success) {
            return data.data.url;
        } else {
            throw new Error('ImgBB upload failed');
        }
    },

    // Alternative: Upload to Cloudinary (Free tier: 25GB)
    async uploadToCloudinary(imageBlob, fileName) {
        const formData = new FormData();
        formData.append('file', imageBlob);
        formData.append('upload_preset', 'your-upload-preset'); // Create in Cloudinary dashboard

        const CLOUD_NAME = 'your-cloud-name'; // Your Cloudinary cloud name

        const response = await fetch(`https://api.cloudinary.com/v1_1/${CLOUD_NAME}/image/upload`, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.secure_url) {
            return data.secure_url;
        } else {
            throw new Error('Cloudinary upload failed');
        }
    },

    // Listen to bills in real-time
    onBillsChange(callback, limit = 50) {
        return db.collection(COLLECTIONS.BILLS)
            .orderBy('createdAt', 'desc')
            .limit(limit)
            .onSnapshot((snapshot) => {
                const bills = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                callback(bills);
            }, (error) => {
                console.error('Error listening to bills:', error);
                callback([]);
            });
    }
};

// Export for use in main script
window.FirebaseService = FirebaseService;
