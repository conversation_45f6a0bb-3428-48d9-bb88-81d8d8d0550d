# 🚀 Hostinger Deployment Guide

## 📋 Prerequisites
- Hostinger hosting account
- Domain name (or subdomain)
- FTP/File Manager access
- PHP support (included in most Hostinger plans)

## 📁 Project Structure for Deployment
```
your-domain.com/
├── index.html
├── styles.css
├── script.js
├── firebase-config.js
├── api/
│   ├── upload-image.php
│   └── delete-image.php
├── uploads/
│   ├── .htaccess
│   └── images/ (created automatically)
└── assets/ (if any)
```

## 🔧 Step-by-Step Deployment

### 1. Prepare Files for Upload
1. **Update Firebase Config** in `firebase-config.js`:
   ```javascript
   // Replace with your actual Firebase credentials
   const firebaseConfig = {
       apiKey: "your-actual-api-key",
       authDomain: "your-project.firebaseapp.com",
       projectId: "your-project-id",
       // ... other config
   };
   ```

2. **Verify Upload URL** in `firebase-config.js`:
   ```javascript
   // Should be relative path (will work on any domain)
   const uploadUrl = '/api/upload-image.php';
   ```

### 2. Upload Files to Hostinger

#### Option A: File Manager (Recommended)
1. Login to **Hostinger Control Panel**
2. Go to **File Manager**
3. Navigate to `public_html/` (or your domain folder)
4. Upload all project files maintaining the folder structure
5. Set permissions:
   - `api/` folder: **755**
   - `uploads/` folder: **755** (will be created automatically)
   - PHP files: **644**
   - HTML/CSS/JS files: **644**

#### Option B: FTP Client
1. Use **FileZilla** or similar FTP client
2. Connect using Hostinger FTP credentials
3. Upload files to `public_html/` directory
4. Set proper permissions as above

### 3. Test the Setup

#### Test Image Upload:
1. Visit your website: `https://your-domain.com`
2. Create a custom currency
3. Upload an image
4. Should see: "UPLOADING IMAGE..." → "SAVED TO CLOUD!"
5. Check if image appears in the bill

#### Verify File Structure:
```bash
# After first upload, you should see:
public_html/
├── uploads/
│   └── images/
│       └── bill_1234567890_abc123def456.jpg
```

### 4. Security Configuration

#### PHP Settings (if needed):
Create `php.ini` in your root directory:
```ini
; Increase upload limits
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 30

; Security settings
allow_url_fopen = Off
allow_url_include = Off
```

#### Additional .htaccess (root directory):
```apache
# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Hide sensitive files
<Files "*.md">
    Order Deny,Allow
    Deny from all
</Files>

<Files ".env*">
    Order Deny,Allow
    Deny from all
</Files>
```

## 🔍 Troubleshooting

### Upload Fails with "500 Error":
1. **Check PHP errors**: Enable error reporting temporarily
2. **File permissions**: Ensure `uploads/` is writable (755)
3. **PHP limits**: Check if file size exceeds server limits
4. **Path issues**: Verify upload directory path is correct

### Images Not Loading:
1. **Check URL**: Verify image URLs are accessible directly
2. **Permissions**: Ensure images are readable (644)
3. **HTTPS**: Make sure images use HTTPS if site uses HTTPS

### CORS Issues:
1. **Headers**: Verify CORS headers in PHP files
2. **Domain**: Check if accessing from correct domain

## 📊 Benefits of Self-Hosting

### Cost Savings:
- ✅ **$0 additional cost** (included in hosting)
- ✅ **No bandwidth limits** (within hosting plan)
- ✅ **No API rate limits**

### Control:
- ✅ **Full control** over files and security
- ✅ **Custom optimization** and processing
- ✅ **No external dependencies**

### Performance:
- ✅ **Same server** as your app (faster uploads)
- ✅ **Custom caching** rules
- ✅ **Optimized for your needs**

## 🔒 Security Best Practices

### File Upload Security:
- ✅ **File type validation** (implemented)
- ✅ **File size limits** (implemented)
- ✅ **Unique filenames** (implemented)
- ✅ **No PHP execution** in uploads (implemented)

### Additional Recommendations:
1. **Regular backups** of uploads folder
2. **Monitor disk usage** to prevent storage issues
3. **Image optimization** (consider adding compression)
4. **Content moderation** for production use

## 🚀 Production Optimizations

### Image Optimization:
```php
// Add to upload-image.php for automatic compression
if ($mimeType === 'image/jpeg') {
    $image = imagecreatefromjpeg($file['tmp_name']);
    imagejpeg($image, $targetPath, 85); // 85% quality
    imagedestroy($image);
}
```

### CDN Integration:
- Consider **Cloudflare** for free CDN
- **Cache static images** for better performance
- **Gzip compression** for faster loading

### Monitoring:
- **Disk space alerts** when uploads folder grows
- **Error logging** for failed uploads
- **Usage statistics** for optimization

## 🎯 Next Steps

1. **Deploy and test** the basic functionality
2. **Set up Firebase** following the Firebase setup guide
3. **Test real-time sync** across multiple devices
4. **Add monitoring** and backup procedures
5. **Consider CDN** for better global performance

Your Meme Printer will now have:
- ✅ **Self-hosted images** on your Hostinger server
- ✅ **Firebase real-time database** for bill data
- ✅ **Complete control** over your infrastructure
- ✅ **Zero external dependencies** for image hosting

This gives you the perfect balance of control, cost-effectiveness, and performance! 🎉
