// Meme Printer App JavaScript
class MemePrinter {
    constructor() {
        this.currentCurrency = 'dogecoin';
        this.isPrinting = false;
        this.soundEnabled = true;
        this.printCount = 0;
        
        this.currencies = {
            dogecoin: {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                symbol: '🐕',
                denomination: 'DOGE',
                value: 'TO THE MOON',
                color: '#f7931a',
                motto: 'Much Money, Very Rich'
            },
            exposure: {
                name: 'Exposure Bucks',
                symbol: '📸',
                denomination: 'EXP',
                value: '∞',
                color: '#e74c3c',
                motto: 'Payment in Exposure'
            },
            netflix: {
                name: 'Netflix Password Pass',
                symbol: '🎬',
                denomination: 'NFLX',
                value: 'UNLIMITED',
                color: '#e50914',
                motto: 'Share the Love'
            },
            crypto: {
                name: 'Crypto Dreams',
                symbol: '₿',
                denomination: 'HODL',
                value: '100K',
                color: '#f7931a',
                motto: 'Diamond Hands Only'
            },
            likes: {
                name: 'Social Likes',
                symbol: '❤️',
                denomination: 'LIKE',
                value: '1M',
                color: '#e1306c',
                motto: 'For the Gram'
            },
            custom: {
                name: 'Custom Currency',
                symbol: '🎨',
                denomination: 'CUSTOM',
                value: 'PRICELESS',
                color: '#667eea',
                motto: 'Your Creation',
                image: null
            }
        };

        this.customImageData = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateScreen('READY TO PRINT');
    }
    
    bindEvents() {
        // Currency selection
        document.querySelectorAll('.currency-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectCurrency(e.target.dataset.currency);
            });
        });
        
        // Print button
        document.getElementById('printBtn').addEventListener('click', () => {
            this.startPrinting();
        });
        
        // PDF button
        document.getElementById('pdfBtn').addEventListener('click', () => {
            this.downloadPDF();
        });
        
        // Sound toggle
        document.getElementById('soundToggle').addEventListener('click', () => {
            this.toggleSound();
        });
        
        // Printer buttons
        document.getElementById('powerBtn').addEventListener('click', () => {
            this.togglePower();
        });

        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.showSettings();
        });

        // Custom currency handlers
        document.getElementById('saveCustomBtn').addEventListener('click', () => {
            this.saveCustomCurrency();
        });

        document.getElementById('customImage').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });

        // Add input listeners for live preview
        ['customName', 'customSymbol', 'customValue', 'customMotto', 'customColor'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.updateCustomPreview();
            });
        });
    }
    
    selectCurrency(currency) {
        this.currentCurrency = currency;

        // Update active button
        document.querySelectorAll('.currency-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-currency="${currency}"]`).classList.add('active');

        // Show/hide custom creator
        const customCreator = document.getElementById('customCreator');
        if (currency === 'custom') {
            customCreator.style.display = 'block';
            this.updateScreen('CUSTOM MODE ACTIVE');
        } else {
            customCreator.style.display = 'none';
            this.updateScreen(`${this.currencies[currency].name.toUpperCase()} SELECTED`);
        }
    }
    
    async startPrinting() {
        if (this.isPrinting) return;
        
        this.isPrinting = true;
        const printBtn = document.getElementById('printBtn');
        const printer = document.querySelector('.printer');
        
        // Update button state
        printBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">PRINTING...</span>';
        printBtn.disabled = true;
        
        // Add printing animation
        printer.classList.add('printing');
        
        // Update screen
        this.updateScreen('PRINTING...');
        
        // Play sound
        this.playSound('printerSound');
        
        // Simulate printing process
        await this.simulatePrinting();
        
        // Generate and display bill
        this.generateBill();
        
        // Reset state
        setTimeout(() => {
            this.isPrinting = false;
            printBtn.innerHTML = '<span class="btn-icon">🖨️</span><span class="btn-text">START PRINTING</span>';
            printBtn.disabled = false;
            printer.classList.remove('printing');
            this.updateScreen('PRINT COMPLETE');
            this.printCount++;
        }, 1000);
    }
    
    async simulatePrinting() {
        return new Promise(resolve => {
            let dots = '';
            const interval = setInterval(() => {
                dots += '.';
                if (dots.length > 3) dots = '';
                this.updateScreen(`PRINTING${dots}`);
            }, 300);
            
            setTimeout(() => {
                clearInterval(interval);
                resolve();
            }, 3000);
        });
    }
    
    generateBill() {
        const currency = this.currencies[this.currentCurrency];
        const output = document.getElementById('printerOutput');
        
        const bill = document.createElement('div');
        bill.className = 'fake-bill';
        bill.style.cssText = `
            width: 260px;
            height: 130px;
            background: linear-gradient(135deg, ${currency.color}, ${this.lightenColor(currency.color, 20)});
            border: 2px solid #333;
            border-radius: 12px;
            padding: 15px;
            margin: 12px 0;
            color: white;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 6px 20px rgba(0,0,0,0.35);
            animation: slideIn 0.5s ease-out;
            position: relative;
            overflow: hidden;
        `;
        
        // For custom currency, prioritize uploaded image over emoji
        let hasCustomImage = false;
        let imageSource = null;

        if (this.currentCurrency === 'custom') {
            imageSource = currency.image || this.customImageData;
            hasCustomImage = !!imageSource;
        }

        bill.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                ${hasCustomImage ?
                    `<img src="${imageSource}" style="width: 38px; height: 38px; object-fit: cover; border-radius: 50%; border: 1px solid white;">` :
                    `<span style="font-size: 1.8rem;">${currency.symbol}</span>`
                }
                <span style="font-weight: bold; font-size: 1rem;">${currency.denomination}</span>
            </div>
            <div style="text-align: center; font-size: 1.4rem; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                ${currency.value}
            </div>
            <div style="font-size: 0.75rem; text-align: center; opacity: 0.85;">
                ${currency.motto}
            </div>
            <div style="position: absolute; top: 8px; right: 12px; font-size: 0.6rem; opacity: 0.7;">
                #${String(this.printCount + 1).padStart(4, '0')}
            </div>
        `;
        
        // Add slide-in animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateY(-100px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
        
        output.appendChild(bill);
        
        // Play paper sound
        setTimeout(() => {
            this.playSound('paperSound');
        }, 500);
        
        // Remove old bills if too many
        const bills = output.querySelectorAll('.fake-bill');
        if (bills.length > 3) {
            bills[0].remove();
        }
    }
    
    async downloadPDF() {
        const currency = this.currencies[this.currentCurrency];
        this.updateScreen('GENERATING PDF...');

        try {
            // Create a temporary bill element for PDF generation
            const tempBill = this.createPDFBill(currency);
            document.body.appendChild(tempBill);

            // Wait a moment for fonts and images to load
            await new Promise(resolve => setTimeout(resolve, 500));

            // Generate canvas from the bill element
            const canvas = await html2canvas(tempBill, {
                backgroundColor: null,
                scale: 3, // Higher resolution
                useCORS: true,
                allowTaint: true
            });

            // Remove temporary element
            document.body.removeChild(tempBill);

            // Create PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: [210, 105] // Roughly bill-sized
            });

            // Add title
            pdf.setFontSize(20);
            pdf.setFont(undefined, 'bold');
            pdf.text('THE MEME PRINTER', 105, 20, { align: 'center' });

            pdf.setFontSize(12);
            pdf.setFont(undefined, 'normal');
            pdf.text('"Feeling broke? Just print your own."', 105, 30, { align: 'center' });

            // Add the bill image
            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 80;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            const x = (210 - imgWidth) / 2;
            const y = 40;

            pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

            // Add details below the bill
            const detailsY = y + imgHeight + 10;
            pdf.setFontSize(10);
            pdf.text(`Currency: ${currency.name}`, 20, detailsY);
            pdf.text(`Value: ${currency.value}`, 20, detailsY + 5);
            pdf.text(`Serial: FAKE-${Date.now()}`, 20, detailsY + 10);
            pdf.text(`Date: ${new Date().toLocaleDateString()}`, 120, detailsY);
            pdf.text(`Motto: "${currency.motto}"`, 120, detailsY + 5);

            // Add warning
            pdf.setFontSize(8);
            pdf.setFont(undefined, 'bold');
            pdf.text('⚠️ WARNING: This is NOT legal tender. Chill.', 105, detailsY + 20, { align: 'center' });
            pdf.setFont(undefined, 'normal');
            pdf.text('This is a parody for entertainment purposes only.', 105, detailsY + 25, { align: 'center' });

            // Download the PDF
            pdf.save(`meme-currency-${this.currentCurrency}-${Date.now()}.pdf`);

            this.updateScreen('PDF DOWNLOADED');

        } catch (error) {
            console.error('PDF generation failed:', error);
            this.updateScreen('PDF ERROR');

            // Fallback to text download
            setTimeout(() => {
                this.downloadTextFallback(currency);
            }, 1000);
        }
    }

    createPDFBill(currency) {
        const bill = document.createElement('div');
        bill.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 400px;
            height: 200px;
            background: linear-gradient(135deg, ${currency.color}, ${this.lightenColor(currency.color, 20)});
            border: 4px solid #333;
            border-radius: 20px;
            padding: 20px;
            color: white;
            font-family: 'Orbitron', monospace;
            font-size: 1.6rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        `;

        const hasCustomImage = currency.image && this.currentCurrency === 'custom';

        bill.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                ${hasCustomImage ?
                    `<img src="${currency.image}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 50%; border: 2px solid white;">` :
                    `<span style="font-size: 3rem;">${currency.symbol}</span>`
                }
                <span style="font-weight: bold; font-size: 1.8rem;">${currency.denomination}</span>
            </div>
            <div style="text-align: center; font-size: 2.4rem; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                ${currency.value}
            </div>
            <div style="font-size: 1.2rem; text-align: center; opacity: 0.9; font-style: italic;">
                "${currency.motto}"
            </div>
            <div style="position: absolute; top: 10px; right: 15px; font-size: 1rem; opacity: 0.7;">
                #${String(this.printCount + 1).padStart(4, '0')}
            </div>
            <div style="position: absolute; bottom: 10px; left: 15px; font-size: 0.8rem; opacity: 0.6;">
                THE MEME PRINTER
            </div>
        `;

        return bill;
    }

    downloadTextFallback(currency) {
        const textContent = `THE MEME PRINTER - Official Fake Currency Certificate

Currency: ${currency.name}
Symbol: ${currency.symbol}
Value: ${currency.value}
Motto: "${currency.motto}"
Serial: FAKE-${Date.now()}
Date: ${new Date().toLocaleDateString()}

⚠️ WARNING: This is NOT legal tender. Chill.
This is a parody for entertainment purposes only.

Printed with love by The Meme Printer
"Feeling broke? Just print your own."`;

        const blob = new Blob([textContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `meme-currency-${this.currentCurrency}-${Date.now()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        const soundBtn = document.getElementById('soundToggle');
        soundBtn.textContent = this.soundEnabled ? '🔊' : '🔇';
    }
    
    togglePower() {
        const screen = document.getElementById('screenText');
        if (screen.textContent === 'SYSTEM OFF') {
            this.updateScreen('SYSTEM ON');
        } else {
            this.updateScreen('SYSTEM OFF');
        }
    }
    
    showSettings() {
        const messages = [
            'SETTINGS MENU',
            'PAPER: LOADED',
            'INK: INFINITE',
            'QUALITY: MEME',
            'LEGAL: NOPE'
        ];
        
        let index = 0;
        const interval = setInterval(() => {
            this.updateScreen(messages[index]);
            index++;
            if (index >= messages.length) {
                clearInterval(interval);
                setTimeout(() => {
                    this.updateScreen('READY TO PRINT');
                }, 1000);
            }
        }, 800);
    }
    
    updateScreen(text) {
        document.getElementById('screenText').textContent = text;
    }

    saveCustomCurrency() {
        this.updateScreen('SAVING...');

        const name = document.getElementById('customName').value.trim();
        const symbol = document.getElementById('customSymbol').value.trim();
        const value = document.getElementById('customValue').value.trim();
        const motto = document.getElementById('customMotto').value.trim();
        const color = document.getElementById('customColor').value;

        // Validation
        if (!name || !symbol || !value || !motto) {
            this.updateScreen('FILL ALL FIELDS');
            return;
        }

        // Update custom currency
        this.currencies.custom = {
            name: name,
            symbol: symbol,
            denomination: name.substring(0, 4).toUpperCase(),
            value: value,
            color: color,
            motto: motto,
            image: this.customImageData
        };

        this.updateScreen('CUSTOM SAVED!');

        // Update button text
        const customBtn = document.querySelector('[data-currency="custom"]');
        customBtn.innerHTML = `${symbol} ${name}`;

        // Auto-hide creator after save
        setTimeout(() => {
            document.getElementById('customCreator').style.display = 'none';
        }, 2000);
    }

    handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.updateScreen('INVALID IMAGE');
            return;
        }

        // Validate file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.updateScreen('IMAGE TOO LARGE');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.customImageData = e.target.result;

            // Show preview
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Custom logo">`;
            preview.classList.remove('empty');

            this.updateScreen('IMAGE UPLOADED');
        };

        reader.readAsDataURL(file);
    }

    updateCustomPreview() {
        const name = document.getElementById('customName').value.trim();
        const symbol = document.getElementById('customSymbol').value.trim();
        const value = document.getElementById('customValue').value.trim();
        const motto = document.getElementById('customMotto').value.trim();
        const color = document.getElementById('customColor').value;

        const preview = document.getElementById('customPreview');
        const previewBill = document.getElementById('previewBill');

        // Show preview if any field has content
        if (name || symbol || value || motto) {
            preview.style.display = 'block';

            const hasImage = this.customImageData;

            previewBill.style.background = `linear-gradient(135deg, ${color}, ${this.lightenColor(color, 20)})`;
            previewBill.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    ${hasImage ?
                        `<img src="${this.customImageData}" style="width: 20px; height: 20px; object-fit: cover; border-radius: 50%;">` :
                        `<span style="font-size: 1rem;">${symbol || '🎨'}</span>`
                    }
                    <span style="font-weight: bold; font-size: 0.5rem;">${name.substring(0, 4).toUpperCase() || 'CUST'}</span>
                </div>
                <div style="text-align: center; font-size: 0.7rem; font-weight: bold;">
                    ${value || 'VALUE'}
                </div>
                <div style="font-size: 0.4rem; text-align: center; opacity: 0.8;">
                    ${motto || 'Your motto here'}
                </div>
            `;
        } else {
            preview.style.display = 'none';
        }
    }
    
    playSound(soundId) {
        if (!this.soundEnabled) return;
        
        // Create a simple beep sound using Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        if (soundId === 'printerSound') {
            // Printer sound simulation
            oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.5);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } else if (soundId === 'paperSound') {
            // Paper sound simulation
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.2);
            gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    }
    
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MemePrinter();
});

// Add some fun easter eggs
document.addEventListener('keydown', (e) => {
    if (e.code === 'Space') {
        e.preventDefault();
        document.getElementById('printBtn').click();
    }
    
    if (e.ctrlKey && e.key === 'm') {
        e.preventDefault();
        const title = document.querySelector('.title');
        title.style.animation = 'none';
        setTimeout(() => {
            title.style.animation = 'glow 0.5s ease-in-out infinite alternate';
        }, 10);
    }
});

// Add some random printer status messages
setInterval(() => {
    const messages = [
        'READY TO PRINT',
        'PAPER LOADED',
        'INK LEVELS: ∞',
        'MEME MODE: ON',
        'LEGAL STATUS: NOPE'
    ];
    
    if (!document.querySelector('.printer').classList.contains('printing')) {
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        document.getElementById('screenText').textContent = randomMessage;
    }
}, 10000);
