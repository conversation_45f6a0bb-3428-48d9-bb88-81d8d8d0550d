<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow DELETE requests
if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['filename']) || empty($input['filename'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Filename is required']);
    exit();
}

$filename = basename($input['filename']); // Security: prevent directory traversal
$uploadDir = '../uploads/images/';
$filePath = $uploadDir . $filename;

// Check if file exists
if (!file_exists($filePath)) {
    http_response_code(404);
    echo json_encode(['error' => 'File not found']);
    exit();
}

// Validate filename pattern (security check)
if (!preg_match('/^bill_\d+_[a-f0-9]{16}\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid filename format']);
    exit();
}

// Delete the file
if (unlink($filePath)) {
    http_response_code(200);
    echo json_encode(['success' => true, 'message' => 'File deleted successfully']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to delete file']);
}
?>
